#!/usr/bin/env python3
"""
OmniCat Egestor Script

This script performs AOI (Area of Interest) queries and exports correlated objects
to YAML format with TLE data. It supports all AOI query filter parameters and
provides configurable output limits.

Usage:
    python -m omnicat.egestor --center 50.0 30.0 400.0 --radius 1000 \
        --time-start "2024-01-01T12:00:00Z" --time-end "2024-01-01T13:00:00Z" \
        --output satellites.yaml --max-objects 100
"""

import argparse
import sys
import yaml
from datetime import datetime
from pathlib import Path
from typing import Any

from omnicat import Settings, get_omnicat
from omnicat.models import AreaOfInterest, Track, Object
from omnicat.models.track_models import get_track_filter, CorrelationStatus
from omnicat.logger import logger


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments for AOI query and export parameters."""
    parser = argparse.ArgumentParser(
        description="Perform AOI queries and export correlated objects to YAML",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic AOI query over Ukraine
  python -m omnicat.egestor --center 50.0 30.0 400.0 --radius 1000 \\
      --time-start "2024-01-01T12:00:00Z" --time-end "2024-01-01T13:00:00Z"
  
  # Filter by specific sources and limit results
  python -m omnicat.egestor --center 0.0 0.0 500.0 --radius 2000 \\
      --time-start "2024-01-01T00:00:00Z" --time-end "2024-01-01T01:00:00Z" \\
      --sources UDL --max-objects 50 --output my_satellites.yaml
        """
    )

    # AOI parameters
    aoi_group = parser.add_argument_group('Area of Interest (AOI) Parameters')
    aoi_group.add_argument(
        '--center',
        nargs=3,
        type=float,
        required=True,
        metavar=('LAT', 'LON', 'ALT'),
        help='Geodetic center of AOI (latitude, longitude, altitude in km)'
    )
    aoi_group.add_argument(
        '--radius',
        type=float,
        required=True,
        help='Radius of AOI in kilometers'
    )
    aoi_group.add_argument(
        '--time-start',
        type=str,
        required=True,
        help='Start time in ISO 8601 format (e.g., "2024-01-01T12:00:00Z")'
    )
    aoi_group.add_argument(
        '--time-end',
        type=str,
        required=True,
        help='End time in ISO 8601 format (e.g., "2024-01-01T13:00:00Z")'
    )

    # Filter parameters
    filter_group = parser.add_argument_group('Track Filter Parameters')
    filter_group.add_argument(
        '--sources',
        nargs='*',
        help='Filter by track sources (e.g., UDL, SPACETRACK)'
    )
    filter_group.add_argument(
        '--object-types',
        nargs='*',
        help='Filter by object types (e.g., PAYLOAD, ROCKET_BODY, DEBRIS)'
    )
    filter_group.add_argument(
        '--country-codes',
        nargs='*',
        help='Filter by country codes (e.g., US, RU, CN)'
    )
    filter_group.add_argument(
        '--allegiances',
        nargs='*',
        help='Filter by allegiance colors (e.g., BLUE, RED, GRAY)'
    )
    filter_group.add_argument(
        '--correlation-statuses',
        nargs='*',
        choices=['Correlated', 'Uncorrelated'],
        help='Filter by correlation status'
    )

    # Output parameters
    output_group = parser.add_argument_group('Output Parameters')
    output_group.add_argument(
        '--output', '-o',
        type=str,
        default='omnicat_export.yaml',
        help='Output YAML file path (default: omnicat_export.yaml)'
    )
    output_group.add_argument(
        '--max-objects',
        type=int,
        help='Maximum number of objects to include in output'
    )

    return parser.parse_args()


def validate_args(args: argparse.Namespace) -> None:
    """Validate command-line arguments and raise errors for invalid values."""
    # Validate time format
    try:
        time_start = datetime.fromisoformat(
            args.time_start.replace('Z', '+00:00'))
        time_end = datetime.fromisoformat(args.time_end.replace('Z', '+00:00'))
    except ValueError as e:
        raise ValueError(f"Invalid time format: {e}")

    if time_start >= time_end:
        raise ValueError("Start time must be before end time")

    # Validate radius
    if args.radius <= 0:
        raise ValueError("Radius must be positive")

    # Validate max_objects
    if args.max_objects is not None and args.max_objects <= 0:
        raise ValueError("max-objects must be positive")

    # Validate output path
    output_path = Path(args.output)
    if output_path.exists() and not output_path.is_file():
        raise ValueError(
            f"Output path exists but is not a file: {args.output}")


def create_aoi_from_args(args: argparse.Namespace) -> AreaOfInterest:
    """Create AreaOfInterest object from command-line arguments."""
    # Parse time strings to datetime objects
    time_start = datetime.fromisoformat(args.time_start.replace('Z', '+00:00'))
    time_end = datetime.fromisoformat(args.time_end.replace('Z', '+00:00'))

    return AreaOfInterest(
        center=args.center,
        radius=args.radius,
        time_start=time_start,
        time_end=time_end
    )


def get_correlated_objects_for_tracks(
    tracks: list[Track],
    track_ids: list[str],
    omnicat
) -> dict[str, tuple[Track, Object]]:
    """
    Get all correlated objects for the given tracks.

    Args:
        tracks: List of Track objects
        track_ids: List of track IDs corresponding to tracks
        omnicat: OmniCat instance for database access

    Returns:
        Dictionary mapping object_id to (Track, Object) tuple
    """
    correlated_objects = {}

    for track, _ in zip(tracks, track_ids):
        if track.object_correlations:
            # Get object IDs from correlations
            object_ids = [
                corr.object_id for corr in track.object_correlations if corr.object_id]

            if object_ids:
                # Retrieve objects from database
                objects = omnicat.neo4j_db.get_objects(object_ids)

                # Map each object to its track
                for obj in objects:
                    if obj and obj.internal_id:
                        correlated_objects[obj.internal_id] = (track, obj)

    return correlated_objects


def get_object_name(obj: Object) -> str:
    """
    Get the best available name for an object.

    Args:
        obj: Object instance

    Returns:
        Object name (common_name if available, otherwise internal_id)
    """
    if obj.common_name:
        return obj.common_name
    elif obj.internal_id:
        return obj.internal_id
    else:
        return "UNKNOWN"


def create_yaml_entry(track: Track, obj: Object, track_id: str) -> dict[str, Any]:
    """
    Create a YAML entry for an object with the specified format.

    Args:
        track: Track object containing TLE data
        obj: Object instance
        track_id: Track ID

    Returns:
        Dictionary representing the YAML entry for this object
    """
    # Convert track to TLE format to get TLE data
    tle_track = track.as_tle()
    tle_data = tle_track.data

    # Create TLE string in the required format
    tle_string = f"{tle_data.line1}\n{tle_data.line2}"

    return {
        'asset_type': 'space_target.yaml/AVG_TGT',
        'TLE': tle_string,
        '_omnicat_track_id': track_id,
        '_omnicat_source': track.source,
        '_omnicat_object_id': obj.internal_id
    }


def export_to_yaml(
    correlated_objects: dict[str, tuple[Track, Object]],
    track_ids_map: dict[str, str],
    output_path: str,
    max_objects: int | None = None
) -> None:
    """
    Export correlated objects to YAML file.

    Args:
        correlated_objects: Dictionary mapping object_id to (Track, Object) tuple
        track_ids_map: Dictionary mapping object_id to track_id
        output_path: Path to output YAML file
        max_objects: Maximum number of objects to export (None for no limit)
    """
    yaml_data = {}

    # Sort objects by name for consistent output
    sorted_objects = sorted(correlated_objects.items(),
                            key=lambda x: get_object_name(x[1][1]))

    # Apply limit if specified
    if max_objects is not None:
        sorted_objects = sorted_objects[:max_objects]

    for object_id, (track, obj) in sorted_objects:
        object_name = get_object_name(obj)
        track_id = track_ids_map.get(object_id, "UNKNOWN")

        yaml_data[object_name] = create_yaml_entry(track, obj, track_id)

    # Write to YAML file
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(yaml_data, f, default_flow_style=False,
                  allow_unicode=True, sort_keys=False)


def create_track_ids_map(tracks: list[Track], track_ids: list[str]) -> dict[str, str]:
    """
    Create a mapping from object_id to track_id for correlated objects.

    Args:
        tracks: List of Track objects
        track_ids: List of track IDs corresponding to tracks

    Returns:
        Dictionary mapping object_id to track_id
    """
    track_ids_map = {}

    for track, track_id in zip(tracks, track_ids):
        if track.object_correlations:
            for corr in track.object_correlations:
                if corr.object_id:
                    track_ids_map[corr.object_id] = track_id

    return track_ids_map


def main() -> None:
    """Main function to execute the egestor script."""
    try:
        # Parse and validate arguments
        args = parse_args()
        validate_args(args)

        logger.info("Starting OmniCat egestor script")
        logger.info(f"AOI center: {args.center}, radius: {args.radius} km")
        logger.info(f"Time window: {args.time_start} to {args.time_end}")

        # Initialize OmniCat
        settings = Settings()
        omnicat = get_omnicat(settings)

        # Create AOI
        aoi = create_aoi_from_args(args)

        # Create track filter
        try:
            track_filter = get_track_filter(
                sources=args.sources,
                object_types=args.object_types,
                country_codes=args.country_codes,
                allegiances=args.allegiances,
                correlation_statuses=args.correlation_statuses
            )
        except ValueError as e:
            logger.error(f"Invalid filter parameters: {e}")
            sys.exit(1)

        # Perform AOI query
        logger.info("Performing AOI query...")
        track_ids, tracks = omnicat.query_filtered_tracks_within_aoi(
            aoi, track_filter)

        logger.info(f"Found {len(tracks)} tracks within AOI")

        if not tracks:
            logger.warning(
                "No tracks found within the specified AOI and filters")
            # Create empty YAML file
            with open(args.output, 'w', encoding='utf-8') as f:
                yaml.dump({}, f)
            logger.info(f"Empty YAML file created: {args.output}")
            return

        # Get correlated objects
        logger.info("Finding correlated objects...")
        correlated_objects = get_correlated_objects_for_tracks(
            tracks, track_ids, omnicat)

        logger.info(f"Found {len(correlated_objects)} correlated objects")

        if not correlated_objects:
            logger.warning("No correlated objects found for the tracks")
            # Create empty YAML file
            with open(args.output, 'w', encoding='utf-8') as f:
                yaml.dump({}, f)
            logger.info(f"Empty YAML file created: {args.output}")
            return

        # Create track IDs mapping
        track_ids_map = create_track_ids_map(tracks, track_ids)

        # Export to YAML
        logger.info(f"Exporting to YAML file: {args.output}")
        export_to_yaml(correlated_objects, track_ids_map,
                       args.output, args.max_objects)

        # Log summary
        exported_count = min(len(correlated_objects),
                             args.max_objects or len(correlated_objects))
        logger.info(
            f"Successfully exported {exported_count} objects to {args.output}")

        if args.max_objects and len(correlated_objects) > args.max_objects:
            logger.info(
                f"Limited output to {args.max_objects} objects (found {len(correlated_objects)} total)")

    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error during execution: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
