# OmniCat AOI Egestor

The AOI Egestor script performs Area of Interest (AOI) queries and exports correlated objects to YAML format with TLE data.

## Features

- **AOI Query Support**: Query tracks within a specified spherical area over a time window
- **Comprehensive Filtering**: Filter by sources, object types, country codes, allegiances, and correlation status
- **Correlated Object Discovery**: Automatically finds all objects correlated with tracks in the AOI
- **YAML Export**: Exports results in the specified OmniCat YAML format with TLE data
- **Result Limiting**: Configurable maximum number of objects to export
- **Error Handling**: Robust error handling for database connections and invalid parameters

## Usage

### Basic Usage

```bash
poetry run python src/omnicat/egestor/aoi_egestor.py \
    --center 50.0 30.0 400.0 \
    --radius 1000 \
    --time-start "2024-01-01T12:00:00Z" \
    --time-end "2024-01-01T13:00:00Z"
```

### Advanced Usage with Filters

```bash
poetry run python src/omnicat/egestor/aoi_egestor.py \
    --center 0.0 0.0 500.0 \
    --radius 2000 \
    --time-start "2024-01-01T00:00:00Z" \
    --time-end "2024-01-01T01:00:00Z" \
    --sources UDL \
    --object-types PAYLOAD ROCKET_BODY \
    --country-codes US RU \
    --correlation-statuses Correlated \
    --max-objects 50 \
    --output my_satellites.yaml
```

## Command Line Arguments

### Required Arguments

- `--center LAT LON ALT`: Geodetic center of AOI (latitude, longitude, altitude in km)
- `--radius RADIUS`: Radius of AOI in kilometers
- `--time-start TIME_START`: Start time in ISO 8601 format
- `--time-end TIME_END`: End time in ISO 8601 format

### Optional Filter Arguments

- `--sources [SOURCES ...]`: Filter by track sources (e.g., UDL, SPACETRACK)
- `--object-types [OBJECT_TYPES ...]`: Filter by object types (e.g., PAYLOAD, ROCKET_BODY, DEBRIS)
- `--country-codes [COUNTRY_CODES ...]`: Filter by country codes (e.g., US, RU, CN)
- `--allegiances [ALLEGIANCES ...]`: Filter by allegiance colors (e.g., BLUE, RED, GRAY)
- `--correlation-statuses [{Correlated,Uncorrelated} ...]`: Filter by correlation status

### Optional Output Arguments

- `--output OUTPUT, -o OUTPUT`: Output YAML file path (default: omnicat_export.yaml)
- `--max-objects MAX_OBJECTS`: Maximum number of objects to include in output

## Output Format

The script exports data in YAML format with the following structure for each object:

```yaml
OBJECT_NAME:
  asset_type: space_target.yaml/PAYLOAD
  TLE: |-
    1 XXXXX... (line 1 of TLE)
    2 XXXXX... (line 2 of TLE)
  _omnicat_track_id: TRK-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  _omnicat_source: UDL
  _omnicat_object_id: OBJ-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
```

Where:
- `OBJECT_NAME` is the object's common name or internal ID
- `asset_type` is determined by the object's type:
  - `PAYLOAD` → `space_target.yaml/PAYLOAD`
  - `ROCKET_BODY` or `ROCKET BODY` → `space_target.yaml/ROCKET_BODY`
  - `DEBRIS` → `space_target.yaml/DEBRIS`
  - `PLATFORM` → `space_target.yaml/PLATFORM`
  - `MANNED` → `space_target.yaml/MANNED`
  - `UNKNOWN` → `space_target.yaml/UNKNOWN`
  - `null` or unknown types → `space_target.yaml/AVG_TGT` (default)
- `TLE` contains the Two-Line Element data in literal block format
- `_omnicat_track_id` is the OmniCat track identifier
- `_omnicat_source` is the data source (e.g., UDL)
- `_omnicat_object_id` is the OmniCat object identifier

## Error Handling

The script includes comprehensive error handling for:
- Invalid command line arguments
- Database connection issues
- Invalid time formats
- Network timeouts
- File I/O errors

## Dependencies

- OmniCat core modules
- PyYAML for YAML output
- Poetry for dependency management

## Notes

- The script requires a running OmniCat database (Neo4j and PostGIS)
- Time values should be in ISO 8601 format with timezone information
- The script will create an empty YAML file if no objects are found
- Results are sorted by object name for consistent output
