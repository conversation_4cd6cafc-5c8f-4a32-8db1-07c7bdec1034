"""
Integration tests for the AOI egestor script.

These tests verify the functionality of the AOI egestor script including:
- Command-line argument parsing and validation
- AOI query execution with correlated object discovery
- YAML output generation with correct format
- Script execution as a subprocess

Note: Tests that require database connections (Neo4j/PostGIS) will fail if the
databases are not running. The help and argument validation tests will pass
without database connections.
"""

import pytest
import yaml
import tempfile
import os
from datetime import datetime, timezone
from pathlib import Path
from unittest.mock import patch
import subprocess

from omnicat import Settings, get_omnicat
from omnicat.models import Track, Object, TLE, ObjectCorrelation
from omnicat.models.track_models import get_track_filter
from omnicat.ingestor import OmnicatIngestor


@pytest.fixture
def temp_output_file():
    """Create a temporary YAML output file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yield f.name
    # Clean up after test
    if os.path.exists(f.name):
        os.unlink(f.name)


@pytest.fixture
def test_objects_with_correlations():
    """Create test objects for correlation testing."""
    return [
        Object(
            source="UDL",
            type="PAYLOAD",
            data_uris=["test://uri1"],
            metadata={
                "International Designator": "2025-001A",
                "satellite_id": "TEST_SAT_1"
            },
            common_name="Test Satellite Alpha",
            country_code="US"
        ),
        Object(
            source="UDL",
            type="ROCKET_BODY",
            data_uris=["test://uri2"],
            metadata={
                "International Designator": "2025-001B",
                "satellite_id": "TEST_SAT_2"
            },
            common_name="Test Satellite Beta",
            country_code="RU"
        )
    ]


@pytest.fixture
def test_tracks_with_correlations(test_objects_with_correlations):
    """Create test tracks with object correlations."""
    base_time = datetime(2025, 2, 12, 21, 30, tzinfo=timezone.utc)

    # Create tracks that will correlate with our test objects
    tracks = []
    for i, obj in enumerate(test_objects_with_correlations):
        track = Track(
            source="UDL",
            format="tle",
            data=TLE(
                line1=f"1 {25544 + i:05d}U 98067A   25043.{50000000 + i*1000000:08d}  .00000000  00000-0  00000-0 0  999{i}",
                line2=f"2 {25544 + i:05d}  51.6416 247.4627 000670{i} 130.5360 325.0288 15.49140040  119{i}"
            ),
            metadata={
                "test": f"correlated_track_{i}",
                "satellite_name": obj.common_name
            },
            object_correlations=[
                ObjectCorrelation(
                    object_id=None,  # Will be set after object ingestion
                    validity_time=base_time,
                    confidence=0.95
                )
            ]
        )
        tracks.append(track)

    return tracks


class TestAoiEgestorIntegration:
    """Integration tests for the AOI egestor script."""

    def test_aoi_egestor_with_correlated_objects(
        self,
        neo4j_db,
        postgis_db,
        aoi_query_truth_data,
        test_objects_with_correlations,
        test_tracks_with_correlations,
        temp_output_file
    ):
        """Test the AOI egestor script with correlated objects."""
        # Extract AOI data from truth data fixture
        aoi = aoi_query_truth_data["aoi"]
        time_window = aoi_query_truth_data["time_window"]

        # Ingest test objects first
        object_ids = neo4j_db.add_objects(test_objects_with_correlations)

        # Update track correlations with actual object IDs
        for i, track in enumerate(test_tracks_with_correlations):
            track.object_correlations[0].object_id = object_ids[i]

        # Ingest tracks with correlations
        omnicat_ingestor = OmnicatIngestor(neo4j_db, postgis_db)
        _ = omnicat_ingestor.ingest_tracks(
            test_tracks_with_correlations,
            time_window["start"],
            time_window["end"]
        )

        # Create OmniCat instance
        omnicat = get_omnicat(Settings(
            neo4j=neo4j_db.settings,
            postgis=postgis_db.settings
        ))

        # Test the egestor functionality directly
        track_filter = get_track_filter(sources=["UDL"])
        result_track_ids, result_tracks = omnicat.query_filtered_tracks_within_aoi(
            aoi, track_filter)

        # Verify we found tracks
        assert len(result_tracks) > 0, "Should find tracks in AOI"

        # Get correlated objects
        correlated_objects = {}
        track_ids_map = {}

        for track, track_id in zip(result_tracks, result_track_ids):
            if track.object_correlations:
                object_ids_to_fetch = [
                    corr.object_id for corr in track.object_correlations if corr.object_id]
                if object_ids_to_fetch:
                    objects = omnicat.get_objects(object_ids_to_fetch)
                    for obj in objects:
                        if obj and obj.internal_id:
                            correlated_objects[obj.internal_id] = (track, obj)
                            track_ids_map[obj.internal_id] = track_id

        # Verify we have correlated objects
        assert len(correlated_objects) > 0, "Should find correlated objects"

        # Test YAML generation
        yaml_data = {}
        for object_id, (track, obj) in correlated_objects.items():
            object_name = obj.common_name if obj.common_name else obj.internal_id
            track_id = track_ids_map.get(object_id, "UNKNOWN")

            # Convert track to TLE format
            tle_track = track.as_tle()
            tle_data = tle_track.data
            tle_string = f"{tle_data.line1}\n{tle_data.line2}"

            yaml_data[object_name] = {
                'asset_type': 'space_target.yaml/AVG_TGT',
                'TLE': tle_string,
                '_omnicat_track_id': track_id,
                '_omnicat_source': track.source,
                '_omnicat_object_id': obj.internal_id
            }

        # Write YAML file
        with open(temp_output_file, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_data, f, default_flow_style=False,
                      allow_unicode=True, sort_keys=False)

        # Verify YAML file was created and has correct structure
        assert os.path.exists(temp_output_file), "YAML file should be created"

        with open(temp_output_file, 'r', encoding='utf-8') as f:
            loaded_yaml = yaml.safe_load(f)

        # Verify YAML structure
        assert len(loaded_yaml) > 0, "YAML should contain objects"

        for object_name, data in loaded_yaml.items():
            # Verify required fields
            assert 'asset_type' in data, f"Object {object_name} missing asset_type"
            assert 'TLE' in data, f"Object {object_name} missing TLE"
            assert '_omnicat_track_id' in data, f"Object {object_name} missing _omnicat_track_id"
            assert '_omnicat_source' in data, f"Object {object_name} missing _omnicat_source"
            assert '_omnicat_object_id' in data, f"Object {object_name} missing _omnicat_object_id"

            # Verify field values
            assert data[
                'asset_type'] == 'space_target.yaml/AVG_TGT', f"Incorrect asset_type for {object_name}"
            assert data['_omnicat_source'] == 'UDL', f"Incorrect source for {object_name}"

            # Verify TLE format
            tle_lines = data['TLE'].strip().split('\n')
            assert len(
                tle_lines) == 2, f"TLE should have 2 lines for {object_name}"
            assert tle_lines[0].startswith(
                '1 '), f"TLE line 1 should start with '1 ' for {object_name}"
            assert tle_lines[1].startswith(
                '2 '), f"TLE line 2 should start with '2 ' for {object_name}"

    def test_aoi_egestor_script_execution(
        self,
        neo4j_db,
        postgis_db,
        aoi_query_truth_data,
        temp_output_file
    ):
        """Test executing the AOI egestor script as a subprocess."""
        # Extract AOI parameters
        aoi = aoi_query_truth_data["aoi"]
        time_window = aoi_query_truth_data["time_window"]

        # Get absolute path to script from project root
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "src" / "omnicat" / "egestor" / "aoi_egestor.py"
        cmd = [
            "poetry", "run", "python", str(script_path),
            "--center", str(aoi.center[0]
                            ), str(aoi.center[1]), str(aoi.center[2]),
            "--radius", str(aoi.radius),
            "--time-start", time_window["start"].isoformat(),
            "--time-end", time_window["end"].isoformat(),
            "--output", temp_output_file,
            "--max-objects", "10"
        ]

        # Mock the database settings to use test databases
        with patch('omnicat.Settings') as mock_settings:
            mock_settings.return_value = Settings(
                neo4j=neo4j_db.settings,
                postgis=postgis_db.settings
            )

            # Execute the script (expect it to fail due to no data, but should create empty YAML)
            try:
                _ = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=str(project_root),
                    env={**os.environ, "PYTHONPATH": str(project_root / "src")}
                )

                # The script should handle empty results gracefully
                assert os.path.exists(
                    temp_output_file), "Output file should be created even with no data"

                # Verify empty YAML structure
                with open(temp_output_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    # Should be empty dict or empty file
                    assert content == '{}' or content == '', "Empty result should produce empty YAML"

            except subprocess.TimeoutExpired:
                pytest.skip(
                    "Script execution timed out - may indicate database connection issues")
            except FileNotFoundError:
                pytest.skip("Script file not found - test environment issue")

    def test_aoi_egestor_argument_validation(self):
        """Test that the AOI egestor script validates arguments correctly."""
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "src" / "omnicat" / "egestor" / "aoi_egestor.py"

        # Test invalid radius
        cmd = [
            "poetry", "run", "python", str(script_path),
            "--center", "50.0", "30.0", "400.0",
            "--radius", "-100",  # Invalid negative radius
            "--time-start", "2024-01-01T12:00:00Z",
            "--time-end", "2024-01-01T13:00:00Z"
        ]

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                cwd=str(project_root),
                env={**os.environ, "PYTHONPATH": str(project_root / "src")}
            )

            # Should exit with error code
            assert result.returncode != 0, "Script should fail with invalid radius"
            assert "Radius must be positive" in result.stderr, "Should show radius validation error"

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("Script execution issue - test environment problem")

    def test_aoi_egestor_help_output(self):
        """Test that the AOI egestor script shows help correctly."""
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "src" / "omnicat" / "egestor" / "aoi_egestor.py"
        cmd = ["poetry", "run", "python", str(script_path), "--help"]

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                cwd=str(project_root),
                env={**os.environ, "PYTHONPATH": str(project_root / "src")}
            )

            # Should exit successfully and show help
            assert result.returncode == 0, "Help should exit successfully"
            assert "Perform AOI queries and export correlated objects to YAML" in result.stdout
            assert "--center" in result.stdout, "Should show center argument"
            assert "--radius" in result.stdout, "Should show radius argument"
            assert "--time-start" in result.stdout, "Should show time-start argument"
            assert "--time-end" in result.stdout, "Should show time-end argument"
            assert "--output" in result.stdout, "Should show output argument"
            assert "--max-objects" in result.stdout, "Should show max-objects argument"

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("Script execution issue - test environment problem")
