"""
Unit tests for the AOI egestor script.

These tests verify functionality that doesn't require database connections:
- Command-line argument parsing and validation
- Help output generation
- Asset type mapping logic
- Script execution without database dependencies
"""

import pytest
import subprocess
import sys
import os
from pathlib import Path


class TestAoiEgestorUnit:
    """Unit tests for the AOI egestor script that don't require database connections."""

    def test_aoi_egestor_argument_validation(self):
        """Test that the AOI egestor script validates arguments correctly."""
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "src" / "omnicat" / "egestor" / "aoi_egestor.py"

        # Test invalid radius
        cmd = [
            "poetry", "run", "python", str(script_path),
            "--center", "50.0", "30.0", "400.0",
            "--radius", "-100",  # Invalid negative radius
            "--time-start", "2024-01-01T12:00:00Z",
            "--time-end", "2024-01-01T13:00:00Z"
        ]

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                cwd=str(project_root),
                env={**os.environ, "PYTHONPATH": str(project_root / "src")}
            )

            # Should exit with error code
            assert result.returncode != 0, "Script should fail with invalid radius"
            assert "Radius must be positive" in result.stderr, "Should show radius validation error"

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("Script execution issue - test environment problem")

    def test_aoi_egestor_help_output(self):
        """Test that the AOI egestor script shows help correctly."""
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "src" / "omnicat" / "egestor" / "aoi_egestor.py"
        cmd = ["poetry", "run", "python", str(script_path), "--help"]

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                cwd=str(project_root),
                env={**os.environ, "PYTHONPATH": str(project_root / "src")}
            )

            # Should exit successfully and show help
            assert result.returncode == 0, "Help should exit successfully"
            assert "Perform AOI queries and export correlated objects to YAML" in result.stdout
            assert "--center" in result.stdout, "Should show center argument"
            assert "--radius" in result.stdout, "Should show radius argument"
            assert "--time-start" in result.stdout, "Should show time-start argument"
            assert "--time-end" in result.stdout, "Should show time-end argument"
            assert "--output" in result.stdout, "Should show output argument"
            assert "--max-objects" in result.stdout, "Should show max-objects argument"

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("Script execution issue - test environment problem")

    def test_asset_type_mapping(self):
        """Test that asset type mapping works correctly for different object types."""
        # Import the function directly
        from pathlib import Path

        # Add the egestor directory to path
        egestor_path = Path(
            __file__).parent.parent.parent.parent / "src" / "omnicat" / "egestor"
        sys.path.insert(0, str(egestor_path))

        from aoi_egestor import get_asset_type_from_object_type

        # Test known object types
        assert get_asset_type_from_object_type(
            "PAYLOAD") == "space_target.yaml/PAYLOAD"
        assert get_asset_type_from_object_type(
            "ROCKET_BODY") == "space_target.yaml/ROCKET_BODY"
        assert get_asset_type_from_object_type(
            "ROCKET BODY") == "space_target.yaml/ROCKET_BODY"  # Handle space format
        assert get_asset_type_from_object_type(
            "DEBRIS") == "space_target.yaml/DEBRIS"
        assert get_asset_type_from_object_type(
            "PLATFORM") == "space_target.yaml/PLATFORM"
        assert get_asset_type_from_object_type(
            "MANNED") == "space_target.yaml/MANNED"
        assert get_asset_type_from_object_type(
            "UNKNOWN") == "space_target.yaml/UNKNOWN"

        # Test case insensitive
        assert get_asset_type_from_object_type(
            "payload") == "space_target.yaml/PAYLOAD"
        assert get_asset_type_from_object_type(
            "Payload") == "space_target.yaml/PAYLOAD"

        # Test unknown/None types default to AVG_TGT
        assert get_asset_type_from_object_type(
            None) == "space_target.yaml/AVG_TGT"
        assert get_asset_type_from_object_type(
            "") == "space_target.yaml/AVG_TGT"
        assert get_asset_type_from_object_type(
            "UNKNOWN_TYPE") == "space_target.yaml/AVG_TGT"

    def test_time_validation(self):
        """Test that time format validation works correctly."""
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "src" / "omnicat" / "egestor" / "aoi_egestor.py"

        # Test invalid time format
        cmd = [
            "poetry", "run", "python", str(script_path),
            "--center", "50.0", "30.0", "400.0",
            "--radius", "1000",
            "--time-start", "invalid-time-format",  # Invalid time format
            "--time-end", "2024-01-01T13:00:00Z"
        ]

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                cwd=str(project_root),
                env={**os.environ, "PYTHONPATH": str(project_root / "src")}
            )

            # Should exit with error code
            assert result.returncode != 0, "Script should fail with invalid time format"
            assert "Invalid time format" in result.stderr, "Should show time format validation error"

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("Script execution issue - test environment problem")

    def test_time_order_validation(self):
        """Test that start time must be before end time."""
        project_root = Path(__file__).parent.parent.parent.parent
        script_path = project_root / "src" / "omnicat" / "egestor" / "aoi_egestor.py"

        # Test start time after end time
        cmd = [
            "poetry", "run", "python", str(script_path),
            "--center", "50.0", "30.0", "400.0",
            "--radius", "1000",
            "--time-start", "2024-01-01T13:00:00Z",  # After end time
            "--time-end", "2024-01-01T12:00:00Z"     # Before start time
        ]

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                cwd=str(project_root),
                env={**os.environ, "PYTHONPATH": str(project_root / "src")}
            )

            # Should exit with error code
            assert result.returncode != 0, "Script should fail when start time is after end time"
            assert "Start time must be before end time" in result.stderr, "Should show time order validation error"

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("Script execution issue - test environment problem")
